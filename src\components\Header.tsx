import Image from "next/image";
import Logo from "../../public/Logo.png";
import SearchIcon from "../../public/Magnifer.png";
import LightIcon from "../../public/Union.png";
import JsIcon from "../../public/js.png";
const Header = () => {
  return (
    <div className="p-6 flex justify-between bg-white rounded-t-2xl  relative border-b border-gray-300">
      <div>
        <Image src={Logo} alt="logo" className="w-50 mt-2  object-contain" />
      </div>
      <div className="flex relative  ">
        <Image src={SearchIcon} alt="search" className="w-5 h-5  object-contain absolute left-3 top-1/2 transform -translate-y-1/2" />
        <input
          type="text"
          className="border border-gray-300 rounded-xl p-4 w-140 pl-10 focus:outline-none bg-[#f4f7f9] text-gray-500"
          placeholder="Search for questions..."
        />
      </div>
      <div className="flex gap-2">
        <Image src={LightIcon} alt="logo" className=" mt-4  object-contain" />
        <Image src={JsIcon} alt="logo" className=" mt-4  object-contain" />
      </div>
    </div>
  );
};
export default Header;
